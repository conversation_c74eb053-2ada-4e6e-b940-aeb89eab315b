package common

import (
	"fmt"
	_ "image/gif"
	_ "image/jpeg"
	_ "image/png"
	"one-api/constant"
	"strings"

	"github.com/gin-gonic/gin"
)

func GetFullRequestURL(baseURL string, requestURL string, channelType int) string {
	// Remove /automodel prefix if request URL starts with /automodel/
	if strings.HasPrefix(requestURL, "/automodel/") {
		requestURL = strings.TrimPrefix(requestURL, "/automodel")
	}

	fullRequestURL := fmt.Sprintf("%s%s", baseURL, requestURL)

	if strings.HasPrefix(baseURL, "https://gateway.ai.cloudflare.com") {
		switch channelType {
		case constant.ChannelTypeOpenAI:
			fullRequestURL = fmt.Sprintf("%s%s", baseURL, strings.TrimPrefix(requestURL, "/v1"))
		case constant.ChannelTypeAzure:
			fullRequestURL = fmt.Sprintf("%s%s", baseURL, strings.TrimPrefix(requestURL, "/openai/deployments"))
		}
	}
	return fullRequestURL
}

func GetAPIVersion(c *gin.Context) string {
	query := c.Request.URL.Query()
	apiVersion := query.Get("api-version")
	if apiVersion == "" {
		apiVersion = c.GetString("api_version")
	}
	return apiVersion
}
